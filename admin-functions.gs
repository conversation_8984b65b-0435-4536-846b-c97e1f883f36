/**
 * POS System Admin Functions
 * Fungsi-fungsi untuk admin panel
 */

/**
 * Get admin statistics for dashboard
 * @return {Object} Response object with statistics
 */
function getAdminStatistics() {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'A<PERSON><PERSON> ditolak');
    }
    
    const stats = {
      totalBarang: getAllData(SHEET_NAMES.BARANG).length,
      totalKategori: getAllData(SHEET_NAMES.KATEGORI).length,
      totalUsers: getAllData(SHEET_NAMES.USERS).length,
      totalPelanggan: getAllData(SHEET_NAMES.PELANGGAN).length,
      totalSatuan: getAllData(SHEET_NAMES.SATUAN).length,
      totalMetodePembayaran: getAllData(SHEET_NAMES.METODE_PEMBAYARAN).length
    };
    
    return createResponse(true, 'Statistik berhasil dimuat', stats);
    
  } catch (error) {
    console.error('Error getting admin statistics:', error);
    return createResponse(false, '<PERSON><PERSON><PERSON><PERSON> k<PERSON>: ' + error.message);
  }
}

/**
 * Get all users (admin only)
 * @return {Object} Response object with users data
 */
function getAllUsers() {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak');
    }
    
    const users = getAllData(SHEET_NAMES.USERS);
    
    // Remove password from response for security
    const safeUsers = users.map(user => [
      user[0], // user_id
      user[1], // username
      '***', // password (hidden)
      user[3], // nama_lengkap
      user[4], // role
      user[5]  // keterangan
    ]);
    
    return createResponse(true, 'Data users berhasil dimuat', safeUsers);
    
  } catch (error) {
    console.error('Error getting all users:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get all categories
 * @return {Object} Response object with categories data
 */
function getAllKategori() {
  try {
    const categories = getAllData(SHEET_NAMES.KATEGORI);
    return createResponse(true, 'Data kategori berhasil dimuat', categories);
    
  } catch (error) {
    console.error('Error getting categories:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Create new category
 * @param {Object} params - Category data
 * @return {Object} Response object
 */
function createKategori(params) {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak');
    }
    
    const { namaKategori, keterangan } = params;
    
    if (!namaKategori) {
      return createResponse(false, 'Nama kategori harus diisi');
    }
    
    // Check if category name already exists
    const existingCategory = searchData(SHEET_NAMES.KATEGORI, 'B', namaKategori);
    if (existingCategory.length > 0) {
      return createResponse(false, 'Nama kategori sudah digunakan');
    }
    
    const kategoriId = getNextId(SHEET_NAMES.KATEGORI);
    const newCategoryData = [
      kategoriId,
      namaKategori,
      keterangan || ''
    ];
    
    const success = insertData(SHEET_NAMES.KATEGORI, newCategoryData);
    
    if (success) {
      return createResponse(true, 'Kategori berhasil dibuat', {
        kategoriId: kategoriId,
        namaKategori: namaKategori
      });
    } else {
      return createResponse(false, 'Gagal membuat kategori');
    }
    
  } catch (error) {
    console.error('Error creating category:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get all units (satuan)
 * @return {Object} Response object with units data
 */
function getAllSatuan() {
  try {
    const units = getAllData(SHEET_NAMES.SATUAN);
    return createResponse(true, 'Data satuan berhasil dimuat', units);
    
  } catch (error) {
    console.error('Error getting units:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Create new unit (satuan)
 * @param {Object} params - Unit data
 * @return {Object} Response object
 */
function createSatuan(params) {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak');
    }
    
    const { namaSatuan, keterangan } = params;
    
    if (!namaSatuan) {
      return createResponse(false, 'Nama satuan harus diisi');
    }
    
    // Check if unit name already exists
    const existingUnit = searchData(SHEET_NAMES.SATUAN, 'B', namaSatuan);
    if (existingUnit.length > 0) {
      return createResponse(false, 'Nama satuan sudah digunakan');
    }
    
    const satuanId = getNextId(SHEET_NAMES.SATUAN);
    const newUnitData = [
      satuanId,
      namaSatuan,
      keterangan || ''
    ];
    
    const success = insertData(SHEET_NAMES.SATUAN, newUnitData);
    
    if (success) {
      return createResponse(true, 'Satuan berhasil dibuat', {
        satuanId: satuanId,
        namaSatuan: namaSatuan
      });
    } else {
      return createResponse(false, 'Gagal membuat satuan');
    }
    
  } catch (error) {
    console.error('Error creating unit:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get all customers (pelanggan)
 * @return {Object} Response object with customers data
 */
function getAllPelanggan() {
  try {
    const customers = getAllData(SHEET_NAMES.PELANGGAN);
    return createResponse(true, 'Data pelanggan berhasil dimuat', customers);
    
  } catch (error) {
    console.error('Error getting customers:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Create new customer
 * @param {Object} params - Customer data
 * @return {Object} Response object
 */
function createPelanggan(params) {
  try {
    const { namaPelanggan, telepon, email, alamat, keterangan } = params;
    
    if (!namaPelanggan) {
      return createResponse(false, 'Nama pelanggan harus diisi');
    }
    
    const pelangganId = getNextId(SHEET_NAMES.PELANGGAN);
    const newCustomerData = [
      pelangganId,
      namaPelanggan,
      telepon || '',
      email || '',
      alamat || '',
      keterangan || ''
    ];
    
    const success = insertData(SHEET_NAMES.PELANGGAN, newCustomerData);
    
    if (success) {
      return createResponse(true, 'Pelanggan berhasil dibuat', {
        pelangganId: pelangganId,
        namaPelanggan: namaPelanggan
      });
    } else {
      return createResponse(false, 'Gagal membuat pelanggan');
    }
    
  } catch (error) {
    console.error('Error creating customer:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get all products (barang)
 * @return {Object} Response object with products data
 */
function getAllBarang() {
  try {
    const products = getAllData(SHEET_NAMES.BARANG);
    return createResponse(true, 'Data barang berhasil dimuat', products);
    
  } catch (error) {
    console.error('Error getting products:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Create new product
 * @param {Object} params - Product data
 * @return {Object} Response object
 */
function createBarang(params) {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak');
    }
    
    const { 
      barcode, 
      namaBarang, 
      kategoriId, 
      satuanId, 
      hargaBeli, 
      hargaJual, 
      stok, 
      keterangan 
    } = params;
    
    // Validation
    if (!namaBarang || !kategoriId || !satuanId || !hargaBeli || !hargaJual) {
      return createResponse(false, 'Nama barang, kategori, satuan, harga beli, dan harga jual harus diisi');
    }
    
    if (barcode && !validateEAN13(barcode)) {
      return createResponse(false, 'Format barcode EAN-13 tidak valid');
    }
    
    // Check if barcode already exists (if provided)
    if (barcode) {
      const existingProduct = searchData(SHEET_NAMES.BARANG, 'B', barcode);
      if (existingProduct.length > 0) {
        return createResponse(false, 'Barcode sudah digunakan');
      }
    }
    
    // Validate kategori exists
    const kategoriData = getDataById(SHEET_NAMES.KATEGORI, kategoriId);
    if (!kategoriData) {
      return createResponse(false, 'Kategori tidak ditemukan');
    }
    
    // Validate satuan exists
    const satuanData = getDataById(SHEET_NAMES.SATUAN, satuanId);
    if (!satuanData) {
      return createResponse(false, 'Satuan tidak ditemukan');
    }
    
    const barangId = getNextId(SHEET_NAMES.BARANG);
    const newProductData = [
      barangId,
      barcode || '',
      namaBarang,
      parseInt(kategoriId),
      parseInt(satuanId),
      parseFloat(hargaBeli),
      parseFloat(hargaJual),
      parseInt(stok) || 0,
      keterangan || ''
    ];
    
    const success = insertData(SHEET_NAMES.BARANG, newProductData);
    
    if (success) {
      return createResponse(true, 'Barang berhasil dibuat', {
        barangId: barangId,
        namaBarang: namaBarang
      });
    } else {
      return createResponse(false, 'Gagal membuat barang');
    }
    
  } catch (error) {
    console.error('Error creating product:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Update product stock
 * @param {Object} params - Stock update data
 * @return {Object} Response object
 */
function updateStok(params) {
  try {
    const { barangId, stokBaru, keterangan } = params;
    
    if (!barangId || stokBaru === undefined) {
      return createResponse(false, 'ID barang dan stok baru harus diisi');
    }
    
    // Get current product data
    const productData = getDataById(SHEET_NAMES.BARANG, barangId);
    if (!productData) {
      return createResponse(false, 'Barang tidak ditemukan');
    }
    
    // Update stock
    productData[7] = parseInt(stokBaru); // stok column
    
    const success = updateDataById(SHEET_NAMES.BARANG, barangId, productData);
    
    if (success) {
      return createResponse(true, 'Stok berhasil diupdate', {
        barangId: barangId,
        stokBaru: stokBaru
      });
    } else {
      return createResponse(false, 'Gagal mengupdate stok');
    }
    
  } catch (error) {
    console.error('Error updating stock:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Delete user (admin only)
 * @param {Object} params - User ID to delete
 * @return {Object} Response object
 */
function deleteUser(params) {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak');
    }
    
    const { userId } = params;
    const currentUser = getCurrentUser();
    
    // Prevent admin from deleting themselves
    if (currentUser.userId == userId) {
      return createResponse(false, 'Tidak dapat menghapus akun sendiri');
    }
    
    const success = deleteDataById(SHEET_NAMES.USERS, userId);
    
    if (success) {
      return createResponse(true, 'User berhasil dihapus');
    } else {
      return createResponse(false, 'Gagal menghapus user');
    }
    
  } catch (error) {
    console.error('Error deleting user:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}
