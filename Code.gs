/**
 * POS System - Main Code File
 * Aplikasi Point of Sale berbasis Google Apps Script
 *
 * Author: AI Assistant
 * Created: 2025-08-22
 */

// Global constants
const SHEET_NAMES = {
  USERS: 'users',
  BARANG: 'barang',
  KATEGORI: 'kategori',
  SATUAN: 'satuan',
  PENJUALAN: 'penjualan',
  PENJUALAN_ITEM: 'penjualan_item',
  PELANGGAN: 'pelanggan',
  METODE_PEMBAYARAN: 'metode_pembayaran'
};

const USER_ROLES = {
  ADMIN: 'administrator',
  KASIR: 'kasir'
};

/**
 * Main function untuk setup awal aplikasi
 * Jalankan function ini pertama kali setelah membuat project
 */
function onInstall() {
  setupDatabase();
  createWebApp();
  console.log('POS System berhasil diinstall!');
}

/**
 * Function untuk membuat web app
 */
function createWebApp() {
  // Setup properties untuk session management
  const scriptProperties = PropertiesService.getScriptProperties();
  scriptProperties.setProperties({
    'APP_NAME': 'POS System',
    'VERSION': '1.0.0',
    'TIMEZONE': 'Asia/Jakarta'
  });
}

/**
 * doGet - Entry point untuk web application
 */
function doGet(e) {
  const page = e.parameter.page || 'login';

  switch(page) {
    case 'login':
      return HtmlService.createTemplateFromFile('login')
        .evaluate()
        .setTitle('POS System - Login')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'admin':
      // Temporary: Skip session check for debugging
      try {
        return HtmlService.createTemplateFromFile('admin')
          .evaluate()
          .setTitle('POS System - Admin Panel')
          .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
      } catch (error) {
        console.error('Error loading admin.html:', error);
        return HtmlService.createHtmlOutput('<h1>Error: admin.html not found</h1>');
      }

    case 'kasir':
      // Temporary: Skip session check for debugging
      try {
        return HtmlService.createTemplateFromFile('kasir')
          .evaluate()
          .setTitle('POS System - Kasir')
          .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
      } catch (error) {
        console.error('Error loading kasir.html:', error);
        return HtmlService.createHtmlOutput('<h1>Error: kasir.html not found</h1>');
      }

    default:
      return redirectToLogin();
  }
}

/**
 * doPost - Handle POST requests
 */
function doPost(e) {
  const action = e.parameter.action;

  try {
    switch(action) {
      case 'login':
        return handleLogin(e.parameter);

      case 'logout':
        return handleLogout();

      default:
        return createResponse(false, 'Action tidak dikenali');
    }
  } catch (error) {
    console.error('Error in doPost:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Include HTML files
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Create standardized response
 */
function createResponse(success, message, data = null) {
  return ContentService
    .createTextOutput(JSON.stringify({
      success: success,
      message: message,
      data: data,
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Redirect to login page
 */
function redirectToLogin() {
  const template = HtmlService.createTemplate(`
    <script>
      window.top.location.href = '<?= getWebAppUrl() ?>?page=login';
    </script>
  `);
  return template.evaluate();
}

/**
 * Get web app URL
 */
function getWebAppUrl() {
  return ScriptApp.getService().getUrl();
}

/**
 * Test function untuk development
 */
function testSetup() {
  console.log('Testing POS System setup...');

  // Test database setup
  try {
    setupDatabase();
    console.log('✓ Database setup berhasil');
  } catch (e) {
    console.error('✗ Database setup gagal:', e);
  }

  // Test utility functions
  try {
    const nextId = getNextId(SHEET_NAMES.USERS);
    console.log('✓ Next ID function berhasil, next ID:', nextId);
  } catch (e) {
    console.error('✗ Next ID function gagal:', e);
  }

  // Test transaction number generation
  try {
    const trxNumber = generateTransactionNumber();
    console.log('✓ Transaction number generation berhasil:', trxNumber);
  } catch (e) {
    console.error('✗ Transaction number generation gagal:', e);
  }

  console.log('Test setup selesai');
}

/**
 * Test login function manually
 */
function testLoginFunction() {
  console.log('Testing login function...');

  // Test hash password
  const testPassword = hashPassword('admin123');
  console.log('Hashed password:', testPassword);

  // Test search user
  const userData = searchData(SHEET_NAMES.USERS, 'B', 'admin');
  console.log('User data found:', userData);

  // Test manual login
  const loginResult = handleLogin({
    username: 'admin',
    password: 'admin123'
  });
  console.log('Login result:', loginResult);

  return loginResult;
}

/**
 * Simple test function for web app
 */
function testConnection() {
  return {
    success: true,
    message: 'Connection successful',
    timestamp: new Date().toISOString()
  };
}

/**
 * Test hash password specifically
 */
function testHashPassword() {
  const password = 'admin123';
  const hashed = hashPassword(password);
  console.log('Original password:', password);
  console.log('Hashed password:', hashed);

  // Get user data from sheet
  const userData = getAllData(SHEET_NAMES.USERS);
  console.log('All users data:', userData);

  // Check if admin user exists
  const adminUser = userData.find(user => user[1] === 'admin');
  console.log('Admin user found:', adminUser);

  if (adminUser) {
    console.log('Stored password hash:', adminUser[2]);
    console.log('New password hash:', hashed);
    console.log('Passwords match:', adminUser[2] === hashed);
  }

  return {
    originalPassword: password,
    hashedPassword: hashed,
    userData: userData,
    adminUser: adminUser
  };
}

/**
 * Reset user data if needed
 */
function resetUserData() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = ss.getSheetByName(SHEET_NAMES.USERS);

    if (!sheet) {
      console.log('Sheet users tidak ditemukan');
      return false;
    }

    // Clear existing data (except headers)
    const lastRow = sheet.getLastRow();
    if (lastRow > 1) {
      sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).clear();
    }

    // Add fresh user data
    const adminPassword = hashPassword('admin123');
    const kasirPassword = hashPassword('kasir123');

    console.log('Admin password hash:', adminPassword);
    console.log('Kasir password hash:', kasirPassword);

    const userData = [
      [1, 'admin', adminPassword, 'Administrator', 'administrator', 'Default admin user'],
      [2, 'kasir1', kasirPassword, 'Kasir Satu', 'kasir', 'Default kasir user']
    ];

    sheet.getRange(2, 1, userData.length, 6).setValues(userData);
    console.log('User data berhasil di-reset');

    return true;
  } catch (error) {
    console.error('Error resetting user data:', error);
    return false;
  }
}

/**
 * Test client login function
 */
function testClientLogin() {
  console.log('Testing client login function...');

  const loginResult = clientLogin({
    username: 'admin',
    password: 'admin123'
  });

  console.log('Client login result:', loginResult);
  return loginResult;
}

/**
 * Test doGet function manually
 */
function testDoGet() {
  console.log('Testing doGet function...');

  // Test login page
  const loginPage = doGet({ parameter: {} });
  console.log('Login page result:', loginPage);

  // Test admin page (simulate logged in admin)
  const userProperties = PropertiesService.getUserProperties();
  userProperties.setProperties({
    'SESSION_DATA': JSON.stringify({
      userId: 1,
      username: 'admin',
      namaLengkap: 'Administrator',
      role: 'administrator',
      loginTime: new Date().toISOString()
    }),
    'SESSION_ACTIVE': 'true'
  });

  const adminPage = doGet({ parameter: { page: 'admin' } });
  console.log('Admin page result:', adminPage);

  return {
    loginPage: loginPage,
    adminPage: adminPage
  };
}

/**
 * Debug session data
 */
function debugSession() {
  const userProperties = PropertiesService.getUserProperties();
  const sessionActive = userProperties.getProperty('SESSION_ACTIVE');
  const sessionData = userProperties.getProperty('SESSION_DATA');

  console.log('Session active:', sessionActive);
  console.log('Session data:', sessionData);

  if (sessionData) {
    try {
      const parsed = JSON.parse(sessionData);
      console.log('Parsed session data:', parsed);
    } catch (e) {
      console.log('Error parsing session data:', e);
    }
  }

  console.log('isUserLoggedIn():', isUserLoggedIn());
  console.log('hasRole(ADMIN):', hasRole(USER_ROLES.ADMIN));

  return {
    sessionActive: sessionActive,
    sessionData: sessionData,
    isLoggedIn: isUserLoggedIn(),
    hasAdminRole: hasRole(USER_ROLES.ADMIN)
  };
}