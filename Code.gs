/**
 * POS System - Main Code File
 * Aplikasi Point of Sale berbasis Google Apps Script
 *
 * Author: AI Assistant
 * Created: 2025-08-22
 */

// Global constants
const SHEET_NAMES = {
  USERS: 'users',
  BARANG: 'barang',
  KATEGORI: 'kategori',
  SATUAN: 'satuan',
  PENJUALAN: 'penjualan',
  PENJUALAN_ITEM: 'penjualan_item',
  PELANGGAN: 'pelanggan',
  METODE_PEMBAYARAN: 'metode_pembayaran'
};

const USER_ROLES = {
  ADMIN: 'administrator',
  KASIR: 'kasir'
};

/**
 * Main function untuk setup awal aplikasi
 * Jalankan function ini pertama kali setelah membuat project
 */
function onInstall() {
  setupDatabase();
  createWebApp();
  console.log('POS System berhasil diinstall!');
}

/**
 * Function untuk membuat web app
 */
function createWebApp() {
  // Setup properties untuk session management
  const scriptProperties = PropertiesService.getScriptProperties();
  scriptProperties.setProperties({
    'APP_NAME': 'POS System',
    'VERSION': '1.0.0',
    'TIMEZONE': 'Asia/Jakarta'
  });
}

/**
 * doGet - Entry point untuk web application
 */
function doGet(e) {
  const page = e.parameter.page || 'login';

  switch(page) {
    case 'login':
      return HtmlService.createTemplateFromFile('login')
        .evaluate()
        .setTitle('POS System - Login')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'admin':
      // Check if user is logged in and has admin role
      if (!isUserLoggedIn() || !hasRole(USER_ROLES.ADMIN)) {
        return redirectToLogin();
      }
      return HtmlService.createTemplateFromFile('admin')
        .evaluate()
        .setTitle('POS System - Admin Panel')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'kasir':
      // Check if user is logged in
      if (!isUserLoggedIn()) {
        return redirectToLogin();
      }
      return HtmlService.createTemplateFromFile('kasir')
        .evaluate()
        .setTitle('POS System - Kasir')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    default:
      return redirectToLogin();
  }
}

/**
 * doPost - Handle POST requests
 */
function doPost(e) {
  const action = e.parameter.action;

  try {
    switch(action) {
      case 'login':
        return handleLogin(e.parameter);

      case 'logout':
        return handleLogout();

      default:
        return createResponse(false, 'Action tidak dikenali');
    }
  } catch (error) {
    console.error('Error in doPost:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Include HTML files
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Create standardized response
 */
function createResponse(success, message, data = null) {
  return ContentService
    .createTextOutput(JSON.stringify({
      success: success,
      message: message,
      data: data,
      timestamp: new Date().toISOString()
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Redirect to login page
 */
function redirectToLogin() {
  const template = HtmlService.createTemplate(`
    <script>
      window.top.location.href = '<?= getWebAppUrl() ?>?page=login';
    </script>
  `);
  return template.evaluate();
}

/**
 * Get web app URL
 */
function getWebAppUrl() {
  return ScriptApp.getService().getUrl();
}

/**
 * Test function untuk development
 */
function testSetup() {
  console.log('Testing POS System setup...');

  // Test database setup
  try {
    setupDatabase();
    console.log('✓ Database setup berhasil');
  } catch (e) {
    console.error('✗ Database setup gagal:', e);
  }

  // Test utility functions
  try {
    const nextId = getNextId(SHEET_NAMES.USERS);
    console.log('✓ Next ID function berhasil, next ID:', nextId);
  } catch (e) {
    console.error('✗ Next ID function gagal:', e);
  }

  // Test transaction number generation
  try {
    const trxNumber = generateTransactionNumber();
    console.log('✓ Transaction number generation berhasil:', trxNumber);
  } catch (e) {
    console.error('✗ Transaction number generation gagal:', e);
  }

  console.log('Test setup selesai');
}

/**
 * Test login function manually
 */
function testLoginFunction() {
  console.log('Testing login function...');

  // Test hash password
  const testPassword = hashPassword('admin123');
  console.log('Hashed password:', testPassword);

  // Test search user
  const userData = searchData(SHEET_NAMES.USERS, 'B', 'admin');
  console.log('User data found:', userData);

  // Test manual login
  const loginResult = handleLogin({
    username: 'admin',
    password: 'admin123'
  });
  console.log('Login result:', loginResult);

  return loginResult;
}

/**
 * Simple test function for web app
 */
function testConnection() {
  return {
    success: true,
    message: 'Connection successful',
    timestamp: new Date().toISOString()
  };
}