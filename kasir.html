<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Kasir</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .left-panel {
            flex: 2;
            background: white;
            padding: 1rem;
            overflow-y: auto;
        }

        .right-panel {
            flex: 1;
            background: #f8f9fa;
            padding: 1rem;
            border-left: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
        }

        .search-section {
            margin-bottom: 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #28a745;
        }

        .barcode-input {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .product-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #28a745;
        }

        .product-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .product-price {
            color: #28a745;
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .product-stock {
            color: #6c757d;
            font-size: 0.8rem;
        }

        .cart-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .cart-header {
            background: #28a745;
            color: white;
            padding: 1rem;
            border-radius: 8px 8px 0 0;
            text-align: center;
            font-weight: bold;
        }

        .cart-items {
            flex: 1;
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            overflow-y: auto;
            max-height: 300px;
        }

        .cart-item {
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }

        .item-price {
            color: #28a745;
            font-size: 0.8rem;
        }

        .item-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .qty-btn {
            background: #6c757d;
            color: white;
            border: none;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .qty-btn:hover {
            background: #5a6268;
        }

        .qty-input {
            width: 40px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.25rem;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.7rem;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .cart-summary {
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 1rem;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .summary-row.total {
            font-weight: bold;
            font-size: 1.1rem;
            color: #28a745;
            border-top: 1px solid #dee2e6;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }

        .payment-section {
            margin-top: 1rem;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .payment-method {
            padding: 0.5rem;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s;
            font-size: 0.8rem;
        }

        .payment-method.active {
            border-color: #28a745;
            background-color: #d4edda;
        }

        .payment-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }

        .checkout-btn {
            width: 100%;
            padding: 1rem;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .checkout-btn:hover {
            background: #218838;
        }

        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .alert {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .empty-cart {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .left-panel, .right-panel {
                flex: none;
                height: 50vh;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>POS System - Kasir</h1>
        <div class="user-info">
            <span id="userInfo">Loading...</span>
            <button class="logout-btn" onclick="handleLogout()">Logout</button>
        </div>
    </div>

    <div class="main-container">
        <!-- Left Panel - Products -->
        <div class="left-panel">
            <div id="alert" class="alert"></div>
            
            <div class="search-section">
                <input 
                    type="text" 
                    id="searchInput" 
                    class="search-input" 
                    placeholder="Cari produk atau scan barcode..."
                    autocomplete="off"
                >
            </div>

            <div id="productsGrid" class="products-grid">
                <div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: #6c757d;">
                    Loading products...
                </div>
            </div>
        </div>

        <!-- Right Panel - Cart & Checkout -->
        <div class="right-panel">
            <div class="cart-section">
                <div class="cart-header">
                    Keranjang Belanja
                </div>
                
                <div id="cartItems" class="cart-items">
                    <div class="empty-cart">
                        <p>Keranjang kosong</p>
                        <small>Pilih produk untuk memulai transaksi</small>
                    </div>
                </div>

                <div class="cart-summary">
                    <div class="summary-row">
                        <span>Subtotal:</span>
                        <span id="subtotal">Rp 0</span>
                    </div>
                    <div class="summary-row">
                        <span>Diskon:</span>
                        <span id="discount">Rp 0</span>
                    </div>
                    <div class="summary-row">
                        <span>Pajak:</span>
                        <span id="tax">Rp 0</span>
                    </div>
                    <div class="summary-row total">
                        <span>Total:</span>
                        <span id="total">Rp 0</span>
                    </div>
                </div>
            </div>

            <div class="payment-section">
                <h4 style="margin-bottom: 0.5rem;">Metode Pembayaran</h4>
                <div class="payment-methods">
                    <div class="payment-method active" data-method="cash">Tunai</div>
                    <div class="payment-method" data-method="card">Kartu</div>
                    <div class="payment-method" data-method="digital">Digital</div>
                    <div class="payment-method" data-method="mixed">Kombinasi</div>
                </div>

                <div id="paymentInputs">
                    <input type="number" id="cashAmount" class="payment-input" placeholder="Jumlah tunai" step="0.01">
                </div>

                <div style="margin-bottom: 1rem;">
                    <strong>Kembalian: <span id="change">Rp 0</span></strong>
                </div>

                <button id="checkoutBtn" class="checkout-btn" onclick="processCheckout()">
                    CHECKOUT
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let products = [];
        let cart = [];
        let currentPaymentMethod = 'cash';
        let currentUser = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializeKasir();
            setupEventListeners();
        });

        function initializeKasir() {
            validateSession();
            loadProducts();
        }

        function setupEventListeners() {
            // Search input with barcode scanner support
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', handleSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleBarcodeSearch(this.value);
                }
            });

            // Payment method selection
            document.querySelectorAll('.payment-method').forEach(method => {
                method.addEventListener('click', function() {
                    selectPaymentMethod(this.dataset.method);
                });
            });

            // Payment amount inputs
            document.getElementById('cashAmount').addEventListener('input', calculateChange);
        }

        function validateSession() {
            google.script.run
                .withSuccessHandler(function(response) {
                    if (response.success) {
                        currentUser = response.data.user;
                        document.getElementById('userInfo').textContent = 
                            `${response.data.user.namaLengkap} (${response.data.user.role})`;
                    } else {
                        window.location.href = '?page=login';
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Session validation failed:', error);
                    window.location.href = '?page=login';
                })
                .validateSession();
        }

        function loadProducts() {
            google.script.run
                .withSuccessHandler(function(response) {
                    if (response.success) {
                        products = response.data;
                        displayProducts(products);
                    } else {
                        showAlert(response.message, 'error');
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Failed to load products:', error);
                    showAlert('Gagal memuat data produk', 'error');
                })
                .getAllBarang();
        }

        function displayProducts(productsToShow) {
            const grid = document.getElementById('productsGrid');
            
            if (productsToShow.length === 0) {
                grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: #6c757d;">Tidak ada produk ditemukan</div>';
                return;
            }

            grid.innerHTML = productsToShow.map(product => `
                <div class="product-card" onclick="addToCart(${product[0]})">
                    <h4>${product[2]}</h4>
                    <div class="product-price">Rp ${Number(product[6]).toLocaleString('id-ID')}</div>
                    <div class="product-stock">Stok: ${product[7]}</div>
                </div>
            `).join('');
        }

        function handleSearch(e) {
            const searchTerm = e.target.value.toLowerCase();
            if (searchTerm === '') {
                displayProducts(products);
                return;
            }

            const filteredProducts = products.filter(product => 
                product[2].toLowerCase().includes(searchTerm) || // nama_barang
                product[1].includes(searchTerm) // barcode
            );
            
            displayProducts(filteredProducts);
        }

        function handleBarcodeSearch(barcode) {
            if (!barcode) return;
            
            const product = products.find(p => p[1] === barcode);
            if (product) {
                addToCart(product[0]);
                document.getElementById('searchInput').value = '';
                document.getElementById('searchInput').focus();
            } else {
                showAlert('Produk dengan barcode tersebut tidak ditemukan', 'error');
            }
        }

        function addToCart(productId) {
            const product = products.find(p => p[0] === productId);
            if (!product) return;

            // Check stock
            if (product[7] <= 0) {
                showAlert('Stok produk habis', 'error');
                return;
            }

            const existingItem = cart.find(item => item.productId === productId);
            
            if (existingItem) {
                if (existingItem.quantity >= product[7]) {
                    showAlert('Quantity melebihi stok tersedia', 'error');
                    return;
                }
                existingItem.quantity++;
            } else {
                cart.push({
                    productId: productId,
                    name: product[2],
                    price: product[6],
                    quantity: 1,
                    maxStock: product[7]
                });
            }

            updateCartDisplay();
            calculateTotals();
        }

        function updateCartDisplay() {
            const cartContainer = document.getElementById('cartItems');
            
            if (cart.length === 0) {
                cartContainer.innerHTML = `
                    <div class="empty-cart">
                        <p>Keranjang kosong</p>
                        <small>Pilih produk untuk memulai transaksi</small>
                    </div>
                `;
                return;
            }

            cartContainer.innerHTML = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-price">Rp ${Number(item.price).toLocaleString('id-ID')}</div>
                    </div>
                    <div class="item-controls">
                        <button class="qty-btn" onclick="updateQuantity(${index}, -1)">-</button>
                        <input type="number" class="qty-input" value="${item.quantity}" 
                               onchange="setQuantity(${index}, this.value)" min="1" max="${item.maxStock}">
                        <button class="qty-btn" onclick="updateQuantity(${index}, 1)">+</button>
                        <button class="remove-btn" onclick="removeFromCart(${index})">×</button>
                    </div>
                </div>
            `).join('');
        }

        function updateQuantity(index, change) {
            const item = cart[index];
            const newQuantity = item.quantity + change;
            
            if (newQuantity <= 0) {
                removeFromCart(index);
                return;
            }
            
            if (newQuantity > item.maxStock) {
                showAlert('Quantity melebihi stok tersedia', 'error');
                return;
            }
            
            item.quantity = newQuantity;
            updateCartDisplay();
            calculateTotals();
        }

        function setQuantity(index, quantity) {
            const item = cart[index];
            const newQuantity = parseInt(quantity);
            
            if (newQuantity <= 0) {
                removeFromCart(index);
                return;
            }
            
            if (newQuantity > item.maxStock) {
                showAlert('Quantity melebihi stok tersedia', 'error');
                updateCartDisplay();
                return;
            }
            
            item.quantity = newQuantity;
            calculateTotals();
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            calculateTotals();
        }

        function calculateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discount = 0; // TODO: Implement discount logic
            const tax = 0; // TODO: Implement tax logic
            const total = subtotal - discount + tax;

            document.getElementById('subtotal').textContent = `Rp ${subtotal.toLocaleString('id-ID')}`;
            document.getElementById('discount').textContent = `Rp ${discount.toLocaleString('id-ID')}`;
            document.getElementById('tax').textContent = `Rp ${tax.toLocaleString('id-ID')}`;
            document.getElementById('total').textContent = `Rp ${total.toLocaleString('id-ID')}`;

            calculateChange();
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;
            
            // Update UI
            document.querySelectorAll('.payment-method').forEach(el => el.classList.remove('active'));
            document.querySelector(`[data-method="${method}"]`).classList.add('active');
            
            // Update payment inputs
            updatePaymentInputs(method);
            calculateChange();
        }

        function updatePaymentInputs(method) {
            const container = document.getElementById('paymentInputs');
            
            switch(method) {
                case 'cash':
                    container.innerHTML = '<input type="number" id="cashAmount" class="payment-input" placeholder="Jumlah tunai" step="0.01">';
                    break;
                case 'card':
                    container.innerHTML = '<input type="number" id="cardAmount" class="payment-input" placeholder="Jumlah kartu" step="0.01">';
                    break;
                case 'digital':
                    container.innerHTML = '<input type="number" id="digitalAmount" class="payment-input" placeholder="Jumlah digital" step="0.01">';
                    break;
                case 'mixed':
                    container.innerHTML = `
                        <input type="number" id="cashAmount" class="payment-input" placeholder="Jumlah tunai" step="0.01">
                        <input type="number" id="cardAmount" class="payment-input" placeholder="Jumlah kartu" step="0.01">
                        <input type="number" id="digitalAmount" class="payment-input" placeholder="Jumlah digital" step="0.01">
                    `;
                    break;
            }
            
            // Re-attach event listeners
            container.querySelectorAll('input').forEach(input => {
                input.addEventListener('input', calculateChange);
            });
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            let totalPaid = 0;
            
            const cashInput = document.getElementById('cashAmount');
            const cardInput = document.getElementById('cardAmount');
            const digitalInput = document.getElementById('digitalAmount');
            
            if (cashInput) totalPaid += parseFloat(cashInput.value) || 0;
            if (cardInput) totalPaid += parseFloat(cardInput.value) || 0;
            if (digitalInput) totalPaid += parseFloat(digitalInput.value) || 0;
            
            const change = Math.max(0, totalPaid - total);
            document.getElementById('change').textContent = `Rp ${change.toLocaleString('id-ID')}`;
            
            // Enable/disable checkout button
            const checkoutBtn = document.getElementById('checkoutBtn');
            checkoutBtn.disabled = totalPaid < total || cart.length === 0;
        }

        function processCheckout() {
            if (cart.length === 0) {
                showAlert('Keranjang kosong', 'error');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            let totalPaid = 0;

            const cashInput = document.getElementById('cashAmount');
            const cardInput = document.getElementById('cardAmount');
            const digitalInput = document.getElementById('digitalAmount');

            const cashAmount = parseFloat(cashInput?.value) || 0;
            const cardAmount = parseFloat(cardInput?.value) || 0;
            const digitalAmount = parseFloat(digitalInput?.value) || 0;

            totalPaid = cashAmount + cardAmount + digitalAmount;

            if (totalPaid < total) {
                showAlert('Jumlah pembayaran kurang', 'error');
                return;
            }

            const transactionData = {
                items: cart,
                subtotal: total,
                total: total,
                paymentMethod: currentPaymentMethod,
                cashAmount: cashAmount,
                cardAmount: cardAmount,
                digitalAmount: digitalAmount,
                kembalian: totalPaid - total
            };

            // Disable checkout button
            document.getElementById('checkoutBtn').disabled = true;
            document.getElementById('checkoutBtn').textContent = 'Processing...';

            google.script.run
                .withSuccessHandler(function(response) {
                    if (response.success) {
                        showAlert('Transaksi berhasil!', 'success');
                        // Clear cart
                        cart = [];
                        updateCartDisplay();
                        calculateTotals();
                        // Print receipt if needed
                        if (confirm('Cetak struk?')) {
                            printReceipt(response.data);
                        }
                    } else {
                        showAlert(response.message, 'error');
                    }
                    // Re-enable checkout button
                    document.getElementById('checkoutBtn').disabled = false;
                    document.getElementById('checkoutBtn').textContent = 'CHECKOUT';
                })
                .withFailureHandler(function(error) {
                    console.error('Checkout failed:', error);
                    showAlert('Gagal memproses transaksi', 'error');
                    // Re-enable checkout button
                    document.getElementById('checkoutBtn').disabled = false;
                    document.getElementById('checkoutBtn').textContent = 'CHECKOUT';
                })
                .processTransaction(transactionData);
        }

        function printReceipt(transactionData) {
            // TODO: Implement receipt printing
            console.log('Printing receipt:', transactionData);
            showAlert('Fitur cetak struk sedang dalam pengembangan', 'error');
        }

        function handleLogout() {
            if (confirm('Apakah Anda yakin ingin logout?')) {
                google.script.run
                    .withSuccessHandler(function(response) {
                        if (response.success) {
                            window.location.href = '?page=login';
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('Logout failed:', error);
                    })
                    .handleLogout();
            }
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
