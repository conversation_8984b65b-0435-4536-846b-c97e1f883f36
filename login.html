<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-credentials {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .demo-credentials h4 {
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .demo-credentials p {
            color: #6c757d;
            margin: 0.25rem 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>POS System</h1>
            <p>Silakan login untuk melanjutkan</p>
        </div>

        <div id="alert" class="alert"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn" class="login-btn">
                Login
            </button>
        </form>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Sedang login...</p>
        </div>

        <div class="demo-credentials">
            <h4>Demo Credentials:</h4>
            <p><strong>Admin:</strong> admin / admin123</p>
            <p><strong>Kasir:</strong> kasir1 / kasir123</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const alert = document.getElementById('alert');

            // Check if already logged in
            checkSession();

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleLogin();
            });

            function handleLogin() {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                console.log('=== LOGIN ATTEMPT ===');
                console.log('Username:', username);
                console.log('Password length:', password.length);

                if (!username || !password) {
                    showAlert('Username dan password harus diisi', 'error');
                    return;
                }

                setLoading(true);
                hideAlert();

                console.log('Calling clientLogin...');

                // Use google.script.run with clientLogin function
                google.script.run
                    .withSuccessHandler(function(response) {
                        console.log('=== LOGIN SUCCESS HANDLER ===');
                        console.log('Response received:', response);

                        setLoading(false);

                        if (response && response.success) {
                            console.log('Login successful, preparing redirect...');
                            showAlert(response.message, 'success');

                            // Simple redirect - hard coded
                            const isAdmin = response.data.user.role === 'administrator';
                            const targetPage = isAdmin ? '?page=admin' : '?page=kasir';
                            const currentUrl = window.location.href.split('?')[0];
                            const newUrl = currentUrl + targetPage;

                            console.log('Target page:', targetPage);
                            console.log('New URL:', newUrl);

                            setTimeout(() => {
                                console.log('Executing redirect...');
                                window.location.href = newUrl;
                            }, 1000);
                        } else {
                            console.log('Login failed:', response);
                            showAlert(response ? response.message : 'Login gagal', 'error');
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.log('=== LOGIN FAILURE HANDLER ===');
                        console.error('Login error:', error);
                        setLoading(false);
                        showAlert('Terjadi kesalahan saat login. Silakan coba lagi.', 'error');
                    })
                    .clientLogin({
                        username: username,
                        password: password
                    });
            }

            function checkSession() {
                // Check if user is already logged in
                google.script.run
                    .withSuccessHandler(function(response) {
                        if (response && response.success) {
                            // User is logged in, redirect to appropriate page
                            const redirectUrl = response.data.user.role === 'administrator' ? '?page=admin' : '?page=kasir';
                            window.location.href = redirectUrl;
                        }
                        // If not logged in, just stay on login page
                    })
                    .withFailureHandler(function(error) {
                        console.log('Session check failed:', error);
                        // If session check fails, just stay on login page
                    })
                    .validateSession();
            }

            function setLoading(isLoading) {
                if (isLoading) {
                    loginBtn.disabled = true;
                    loginBtn.textContent = 'Sedang login...';
                    loading.style.display = 'block';
                } else {
                    loginBtn.disabled = false;
                    loginBtn.textContent = 'Login';
                    loading.style.display = 'none';
                }
            }

            function showAlert(message, type) {
                alert.textContent = message;
                alert.className = `alert alert-${type}`;
                alert.style.display = 'block';
            }

            function hideAlert() {
                alert.style.display = 'none';
            }

            // Auto-fill demo credentials when clicking on them
            document.addEventListener('click', function(e) {
                if (e.target.closest('.demo-credentials p')) {
                    const text = e.target.textContent;
                    if (text.includes('admin / admin123')) {
                        document.getElementById('username').value = 'admin';
                        document.getElementById('password').value = 'admin123';
                    } else if (text.includes('kasir1 / kasir123')) {
                        document.getElementById('username').value = 'kasir1';
                        document.getElementById('password').value = 'kasir123';
                    }
                }
            });
        });
    </script>
</body>
</html>
