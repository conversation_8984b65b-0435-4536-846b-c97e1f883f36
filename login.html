<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-credentials {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .demo-credentials h4 {
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .demo-credentials p {
            color: #6c757d;
            margin: 0.25rem 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>POS System</h1>
            <p>Silakan login untuk melanjutkan</p>
        </div>

        <div id="alert" class="alert"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required value="admin">
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required value="admin123">
            </div>

            <button type="submit" id="loginBtn" class="login-btn">
                Login
            </button>
        </form>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Sedang login...</p>
        </div>

        <div class="demo-credentials">
            <h4>Demo Credentials:</h4>
            <p><strong>Admin:</strong> admin / admin123</p>
            <p><strong>Kasir:</strong> kasir1 / kasir123</p>
            <p><small>Credentials sudah diisi otomatis untuk demo</small></p>

            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
                <h4>Debug Tools:</h4>
                <button onclick="testDirectAccess()" style="margin: 5px; padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 4px;">
                    Test Direct Admin Access
                </button>
                <button onclick="testSessionCheck()" style="margin: 5px; padding: 8px 12px; background: #17a2b8; color: white; border: none; border-radius: 4px;">
                    Test Session Check
                </button>
            </div>
        </div>
    </div>

    <script>
        // Advanced POS Login System with Module Pattern
        (function() {
            'use strict';

            // Main login handler
            function handleLogin(e) {
                e.preventDefault();
                console.log('[POS] Login form submitted');

                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    alert('Username dan password harus diisi');
                    return false;
                }

                console.log('[POS] Attempting login for:', username);

                // Disable form
                const btn = document.getElementById('loginBtn');
                btn.disabled = true;
                btn.textContent = 'Sedang login...';

                // Call Google Apps Script with simple login (no session)
                google.script.run
                    .withSuccessHandler(function(response) {
                        console.log('[POS] Simple login response:', response);

                        btn.disabled = false;
                        btn.textContent = 'Login';

                        if (response && response.success) {
                            alert('Login berhasil! Mengalihkan halaman...');

                            // Build redirect URL
                            const baseUrl = window.location.href.split('?')[0];
                            const isAdmin = response.data.user.role === 'administrator';
                            const page = isAdmin ? 'admin' : 'kasir';
                            const redirectUrl = baseUrl + '?page=' + page;

                            console.log('[POS] Redirecting to:', redirectUrl);

                            // Redirect immediately
                            window.location.href = redirectUrl;

                        } else {
                            alert('Login gagal: ' + (response ? response.message : 'Unknown error'));
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('[POS] Simple login error:', error);
                        btn.disabled = false;
                        btn.textContent = 'Login';
                        alert('Terjadi kesalahan saat login: ' + error.toString());
                    })
                    .simpleLogin({
                        username: username,
                        password: password
                    });

                return false;
            }

            // Initialize when DOM is ready
            function init() {
                console.log('[POS] Initializing login system');

                // Setup form handler
                const form = document.getElementById('loginForm');
                if (form) {
                    form.addEventListener('submit', handleLogin);
                    console.log('[POS] Form event listener attached');
                }

                // Temporarily disable session check to debug
                console.log('[POS] Session check disabled for debugging');
                console.log('[POS] Login page ready for manual login');
            }

            // Start initialization
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', init);
            } else {
                init();
            }

        })();

        // Global debug functions
        function testDirectAccess() {
            console.log('[DEBUG] Testing direct admin access');
            const currentUrl = window.location.href.split('?')[0];
            const adminUrl = currentUrl + '?page=admin';
            console.log('[DEBUG] Navigating to:', adminUrl);
            window.location.href = adminUrl;
        }

        function testSessionCheck() {
            console.log('[DEBUG] Testing session check manually');
            google.script.run
                .withSuccessHandler(function(response) {
                    console.log('[DEBUG] Session response:', response);
                    alert('Session check result: ' + JSON.stringify(response, null, 2));
                })
                .withFailureHandler(function(error) {
                    console.error('[DEBUG] Session check error:', error);
                    alert('Session check failed: ' + error);
                })
                .clientValidateSession();
        }
    </script>
</body>
</html>
