/**
 * POS System Kasir Functions
 * Fungsi-fungsi untuk interface kasir
 */

/**
 * Process transaction
 * @param {Object} transactionData - Data transaksi
 * @return {Object} Response object
 */
function processTransaction(transactionData) {
  try {
    // Check if user is logged in
    if (!isUserLoggedIn()) {
      return createResponse(false, 'User tidak login');
    }
    
    const currentUser = getCurrentUser();
    const { items, subtotal, total, paymentMethod, cashAmount, cardAmount, digitalAmount, kembalian } = transactionData;
    
    // Validation
    if (!items || items.length === 0) {
      return createResponse(false, 'Tidak ada item dalam transaksi');
    }
    
    if (total <= 0) {
      return createResponse(false, 'Total transaksi tidak valid');
    }
    
    // Acquire lock untuk concurrent access
    if (!acquireLock('transaction_lock', 30)) {
      return createResponse(false, 'Sistem sedang sibuk, silakan coba lagi');
    }
    
    try {
      // Generate transaction data
      const penjualanId = getNextId(SHEET_NAMES.PENJUALAN);
      const nomorTransaksi = generateTransactionNumber();
      const now = new Date();
      
      // Determine payment method ID
      let metodePembayaranId;
      if (paymentMethod === 'mixed' || (cashAmount > 0 && (cardAmount > 0 || digitalAmount > 0))) {
        metodePembayaranId = 99; // Kombinasi
      } else if (cashAmount > 0) {
        metodePembayaranId = 1; // Tunai
      } else if (cardAmount > 0) {
        metodePembayaranId = 2; // Kartu (default to debit)
      } else if (digitalAmount > 0) {
        metodePembayaranId = 4; // Default to OVO
      } else {
        metodePembayaranId = 1; // Default to cash
      }
      
      // Validate stock and prepare items
      const validatedItems = [];
      for (let item of items) {
        const productData = getDataById(SHEET_NAMES.BARANG, item.productId);
        if (!productData) {
          releaseLock();
          return createResponse(false, `Produk ${item.name} tidak ditemukan`);
        }
        
        if (productData[7] < item.quantity) { // Check stock
          releaseLock();
          return createResponse(false, `Stok ${item.name} tidak mencukupi`);
        }
        
        validatedItems.push({
          productId: item.productId,
          productData: productData,
          quantity: item.quantity,
          hargaBeli: productData[5],
          hargaJual: productData[6],
          subtotalItem: productData[6] * item.quantity
        });
      }
      
      // Insert penjualan record
      const penjualanData = [
        penjualanId,
        nomorTransaksi,
        now,
        currentUser.userId,
        1, // Default pelanggan umum
        subtotal,
        0, // diskon_persen
        0, // diskon_nominal
        0, // pajak_persen
        0, // pajak_nominal
        total,
        metodePembayaranId,
        cashAmount || 0,
        cardAmount || 0,
        digitalAmount || 0,
        kembalian || 0,
        '', // catatan
        formatTime(now),
        formatDate(now)
      ];
      
      const penjualanSuccess = insertData(SHEET_NAMES.PENJUALAN, penjualanData);
      if (!penjualanSuccess) {
        releaseLock();
        return createResponse(false, 'Gagal menyimpan data penjualan');
      }
      
      // Insert penjualan_item records and update stock
      for (let item of validatedItems) {
        // Insert penjualan_item
        const penjualanItemId = getNextId(SHEET_NAMES.PENJUALAN_ITEM);
        const penjualanItemData = [
          penjualanItemId,
          penjualanId,
          item.productId,
          item.hargaBeli,
          item.hargaJual,
          item.quantity,
          0, // diskon_item_persen
          0, // diskon_item_nominal
          item.subtotalItem
        ];
        
        const itemSuccess = insertData(SHEET_NAMES.PENJUALAN_ITEM, penjualanItemData);
        if (!itemSuccess) {
          releaseLock();
          return createResponse(false, `Gagal menyimpan item ${item.productData[2]}`);
        }
        
        // Update stock
        item.productData[7] = item.productData[7] - item.quantity; // Reduce stock
        const stockUpdateSuccess = updateDataById(SHEET_NAMES.BARANG, item.productId, item.productData);
        if (!stockUpdateSuccess) {
          releaseLock();
          return createResponse(false, `Gagal mengupdate stok ${item.productData[2]}`);
        }
      }
      
      releaseLock();
      
      return createResponse(true, 'Transaksi berhasil diproses', {
        penjualanId: penjualanId,
        nomorTransaksi: nomorTransaksi,
        total: total,
        kembalian: kembalian,
        tanggal: formatDate(now),
        jam: formatTime(now)
      });
      
    } catch (error) {
      releaseLock();
      throw error;
    }
    
  } catch (error) {
    console.error('Error processing transaction:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get product by barcode
 * @param {Object} params - Barcode parameter
 * @return {Object} Response object
 */
function getProductByBarcode(params) {
  try {
    const { barcode } = params;
    
    if (!barcode) {
      return createResponse(false, 'Barcode harus diisi');
    }
    
    const products = searchData(SHEET_NAMES.BARANG, 'B', barcode);
    
    if (products.length === 0) {
      return createResponse(false, 'Produk tidak ditemukan');
    }
    
    const product = products[0];
    
    // Get category and unit names
    const kategori = getDataById(SHEET_NAMES.KATEGORI, product[3]);
    const satuan = getDataById(SHEET_NAMES.SATUAN, product[4]);
    
    return createResponse(true, 'Produk ditemukan', {
      barangId: product[0],
      barcode: product[1],
      namaBarang: product[2],
      kategori: kategori ? kategori[1] : 'Unknown',
      satuan: satuan ? satuan[1] : 'Unknown',
      hargaBeli: product[5],
      hargaJual: product[6],
      stok: product[7],
      keterangan: product[8]
    });
    
  } catch (error) {
    console.error('Error getting product by barcode:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Search products
 * @param {Object} params - Search parameters
 * @return {Object} Response object
 */
function searchProducts(params) {
  try {
    const { searchTerm } = params;
    
    if (!searchTerm) {
      return getAllBarang();
    }
    
    // Search in product name and barcode
    const nameResults = searchData(SHEET_NAMES.BARANG, 'C', searchTerm);
    const barcodeResults = searchData(SHEET_NAMES.BARANG, 'B', searchTerm);
    
    // Combine and deduplicate results
    const allResults = [...nameResults, ...barcodeResults];
    const uniqueResults = allResults.filter((item, index, self) => 
      index === self.findIndex(t => t[0] === item[0])
    );
    
    return createResponse(true, 'Pencarian selesai', uniqueResults);
    
  } catch (error) {
    console.error('Error searching products:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get transaction history for current user
 * @param {Object} params - Parameters (limit, offset)
 * @return {Object} Response object
 */
function getTransactionHistory(params = {}) {
  try {
    const currentUser = getCurrentUser();
    if (!currentUser) {
      return createResponse(false, 'User tidak login');
    }
    
    const { limit = 50, offset = 0 } = params;
    
    // Get all transactions for current user
    const allTransactions = getAllData(SHEET_NAMES.PENJUALAN);
    const userTransactions = allTransactions.filter(transaction => 
      transaction[3] == currentUser.userId // user_id column
    );
    
    // Sort by date descending
    userTransactions.sort((a, b) => new Date(b[2]) - new Date(a[2]));
    
    // Apply pagination
    const paginatedTransactions = userTransactions.slice(offset, offset + limit);
    
    return createResponse(true, 'Riwayat transaksi berhasil dimuat', {
      transactions: paginatedTransactions,
      total: userTransactions.length,
      limit: limit,
      offset: offset
    });
    
  } catch (error) {
    console.error('Error getting transaction history:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Get transaction details
 * @param {Object} params - Transaction ID parameter
 * @return {Object} Response object
 */
function getTransactionDetails(params) {
  try {
    const { penjualanId } = params;
    
    if (!penjualanId) {
      return createResponse(false, 'ID penjualan harus diisi');
    }
    
    // Get transaction header
    const transaction = getDataById(SHEET_NAMES.PENJUALAN, penjualanId);
    if (!transaction) {
      return createResponse(false, 'Transaksi tidak ditemukan');
    }
    
    // Get transaction items
    const allItems = getAllData(SHEET_NAMES.PENJUALAN_ITEM);
    const transactionItems = allItems.filter(item => item[1] == penjualanId);
    
    // Get product details for each item
    const itemsWithDetails = transactionItems.map(item => {
      const product = getDataById(SHEET_NAMES.BARANG, item[2]);
      return {
        penjualanItemId: item[0],
        barangId: item[2],
        namaBarang: product ? product[2] : 'Unknown',
        hargaBeli: item[3],
        hargaJual: item[4],
        quantity: item[5],
        diskonItemPersen: item[6],
        diskonItemNominal: item[7],
        subtotalItem: item[8]
      };
    });
    
    // Get user info
    const user = getDataById(SHEET_NAMES.USERS, transaction[3]);
    
    // Get customer info
    const customer = getDataById(SHEET_NAMES.PELANGGAN, transaction[4]);
    
    // Get payment method info
    const paymentMethod = getDataById(SHEET_NAMES.METODE_PEMBAYARAN, transaction[11]);
    
    return createResponse(true, 'Detail transaksi berhasil dimuat', {
      transaction: {
        penjualanId: transaction[0],
        nomorTransaksi: transaction[1],
        tanggalTransaksi: transaction[2],
        kasir: user ? user[3] : 'Unknown',
        pelanggan: customer ? customer[1] : 'Unknown',
        subtotal: transaction[5],
        diskonPersen: transaction[6],
        diskonNominal: transaction[7],
        pajakPersen: transaction[8],
        pajakNominal: transaction[9],
        totalBayar: transaction[10],
        metodePembayaran: paymentMethod ? paymentMethod[1] : 'Unknown',
        jumlahCash: transaction[12],
        jumlahCard: transaction[13],
        jumlahDigital: transaction[14],
        kembalian: transaction[15],
        catatan: transaction[16],
        jam: transaction[17],
        tanggal: transaction[18]
      },
      items: itemsWithDetails
    });
    
  } catch (error) {
    console.error('Error getting transaction details:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}
