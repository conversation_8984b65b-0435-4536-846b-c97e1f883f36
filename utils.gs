/**
 * POS System Utility Functions
 * Fungsi-fungsi helper yang digunakan di seluruh aplikasi
 */

/**
 * Get next auto increment ID for a sheet
 * @param {string} sheetName - Nama sheet
 * @param {string} idColumn - Nama kolom ID (default: kolom A)
 * @return {number} Next ID
 */
function getNextId(sheetName, idColumn = 'A') {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return 1; // First record
  }
  
  const range = sheet.getRange(`${idColumn}2:${idColumn}${lastRow}`);
  const values = range.getValues().flat();
  const maxId = Math.max(...values.filter(val => typeof val === 'number' && !isNaN(val)));
  
  return maxId + 1;
}

/**
 * Generate nomor transaksi
 * Format: TRX-YYYYMMDD-HHMMSS-XXX
 * @return {string} Nomor transaksi
 */
function generateTransactionNumber() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  // Get random 3 digit number
  const random = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
  
  return `TRX-${year}${month}${day}-${hour}${minute}${second}-${random}`;
}

/**
 * Format currency to Indonesian Rupiah
 * @param {number} amount - Jumlah uang
 * @return {string} Formatted currency
 */
function formatCurrency(amount) {
  if (isNaN(amount) || amount === null || amount === undefined) {
    return 'Rp 0';
  }
  
  return 'Rp ' + Number(amount).toLocaleString('id-ID');
}

/**
 * Format date to Indonesian format
 * @param {Date} date - Date object
 * @return {string} Formatted date
 */
function formatDate(date) {
  if (!date || !(date instanceof Date)) {
    date = new Date();
  }
  
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Jakarta'
  };
  
  return date.toLocaleDateString('id-ID', options);
}

/**
 * Format time to Indonesian format
 * @param {Date} date - Date object
 * @return {string} Formatted time
 */
function formatTime(date) {
  if (!date || !(date instanceof Date)) {
    date = new Date();
  }
  
  const options = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Jakarta'
  };
  
  return date.toLocaleTimeString('id-ID', options);
}

/**
 * Validate EAN-13 barcode
 * @param {string} barcode - Barcode to validate
 * @return {boolean} Is valid
 */
function validateEAN13(barcode) {
  if (!barcode || typeof barcode !== 'string') {
    return false;
  }
  
  // Remove any non-digit characters
  barcode = barcode.replace(/\D/g, '');
  
  // Must be exactly 13 digits
  if (barcode.length !== 13) {
    return false;
  }
  
  // Calculate check digit
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    const digit = parseInt(barcode[i]);
    sum += (i % 2 === 0) ? digit : digit * 3;
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(barcode[12]);
}

/**
 * Lock mechanism untuk concurrent access
 * @param {string} lockKey - Unique key untuk lock
 * @param {number} timeoutSeconds - Timeout dalam detik (default: 30)
 * @return {boolean} Berhasil mendapat lock atau tidak
 */
function acquireLock(lockKey, timeoutSeconds = 30) {
  const lock = LockService.getScriptLock();
  try {
    return lock.tryLock(timeoutSeconds * 1000);
  } catch (e) {
    console.error('Error acquiring lock:', e);
    return false;
  }
}

/**
 * Release lock
 */
function releaseLock() {
  const lock = LockService.getScriptLock();
  try {
    lock.releaseLock();
  } catch (e) {
    console.error('Error releasing lock:', e);
  }
}

/**
 * Get data from sheet by ID
 * @param {string} sheetName - Nama sheet
 * @param {number} id - ID yang dicari
 * @param {string} idColumn - Kolom ID (default: A)
 * @return {Array} Data row atau null jika tidak ditemukan
 */
function getDataById(sheetName, id, idColumn = 'A') {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return null;
  }
  
  const data = sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).getValues();
  const idColumnIndex = idColumn.charCodeAt(0) - 65; // Convert A=0, B=1, etc.
  
  for (let i = 0; i < data.length; i++) {
    if (data[i][idColumnIndex] == id) {
      return data[i];
    }
  }
  
  return null;
}

/**
 * Update data by ID
 * @param {string} sheetName - Nama sheet
 * @param {number} id - ID yang akan diupdate
 * @param {Array} newData - Data baru
 * @param {string} idColumn - Kolom ID (default: A)
 * @return {boolean} Berhasil update atau tidak
 */
function updateDataById(sheetName, id, newData, idColumn = 'A') {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return false;
  }
  
  const idColumnIndex = idColumn.charCodeAt(0) - 65;
  const data = sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).getValues();
  
  for (let i = 0; i < data.length; i++) {
    if (data[i][idColumnIndex] == id) {
      const rowNumber = i + 2; // +2 karena mulai dari row 2 dan index 0
      sheet.getRange(rowNumber, 1, 1, newData.length).setValues([newData]);
      return true;
    }
  }
  
  return false;
}

/**
 * Delete data by ID
 * @param {string} sheetName - Nama sheet
 * @param {number} id - ID yang akan dihapus
 * @param {string} idColumn - Kolom ID (default: A)
 * @return {boolean} Berhasil hapus atau tidak
 */
function deleteDataById(sheetName, id, idColumn = 'A') {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return false;
  }
  
  const idColumnIndex = idColumn.charCodeAt(0) - 65;
  const data = sheet.getRange(2, 1, lastRow - 1, 1).getValues();
  
  for (let i = 0; i < data.length; i++) {
    if (data[i][0] == id) {
      const rowNumber = i + 2;
      sheet.deleteRow(rowNumber);
      return true;
    }
  }
  
  return false;
}

/**
 * Get all data from sheet
 * @param {string} sheetName - Nama sheet
 * @param {boolean} includeHeaders - Include headers atau tidak (default: false)
 * @return {Array} Array of data
 */
function getAllData(sheetName, includeHeaders = false) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return [];
  }
  
  const startRow = includeHeaders ? 1 : 2;
  const numRows = includeHeaders ? lastRow : lastRow - 1;
  
  return sheet.getRange(startRow, 1, numRows, sheet.getLastColumn()).getValues();
}

/**
 * Insert new data to sheet
 * @param {string} sheetName - Nama sheet
 * @param {Array} data - Data yang akan diinsert
 * @return {boolean} Berhasil insert atau tidak
 */
function insertData(sheetName, data) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  try {
    const lastRow = sheet.getLastRow();
    sheet.getRange(lastRow + 1, 1, 1, data.length).setValues([data]);
    return true;
  } catch (e) {
    console.error('Error inserting data:', e);
    return false;
  }
}

/**
 * Search data in sheet
 * @param {string} sheetName - Nama sheet
 * @param {string} searchColumn - Kolom yang dicari (A, B, C, etc.)
 * @param {string} searchValue - Nilai yang dicari
 * @return {Array} Array of matching rows
 */
function searchData(sheetName, searchColumn, searchValue) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName(sheetName);
  
  if (!sheet) {
    throw new Error(`Sheet ${sheetName} tidak ditemukan`);
  }
  
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    return [];
  }
  
  const data = sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).getValues();
  const searchColumnIndex = searchColumn.charCodeAt(0) - 65;
  const results = [];
  
  for (let i = 0; i < data.length; i++) {
    if (data[i][searchColumnIndex] && 
        data[i][searchColumnIndex].toString().toLowerCase().includes(searchValue.toLowerCase())) {
      results.push(data[i]);
    }
  }
  
  return results;
}

// Helper function untuk hash password (simple hash untuk demo)
function hashPassword(password) {
  return Utilities.base64Encode(Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password));
}
