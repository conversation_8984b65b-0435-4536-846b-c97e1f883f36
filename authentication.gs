/**
 * POS System Authentication Functions
 * Fungsi-fungsi untuk login, logout, dan session management
 */

/**
 * <PERSON>le login request
 * @param {Object} params - Parameter login (username, password)
 * @return {Object} Response object
 */
function handleLogin(params) {
  const username = params.username;
  const password = params.password;
  
  if (!username || !password) {
    return createResponse(false, 'Username dan password harus diisi');
  }
  
  try {
    // Cari user berdasarkan username
    const userData = searchData(SHEET_NAMES.USERS, 'B', username);
    
    if (userData.length === 0) {
      return createResponse(false, 'Username tidak ditemukan');
    }
    
    const user = userData[0];
    const hashedPassword = hashPassword(password);
    
    // Verify password
    if (user[2] !== hashedPassword) {
      return createResponse(false, 'Password salah');
    }
    
    // Create session
    const sessionData = {
      userId: user[0],
      username: user[1],
      namaLengkap: user[3],
      role: user[4],
      loginTime: new Date().toISOString()
    };
    
    // Store session in PropertiesService
    const userProperties = PropertiesService.getUserProperties();
    userProperties.setProperties({
      'SESSION_DATA': JSON.stringify(sessionData),
      'SESSION_ACTIVE': 'true'
    });
    
    return createResponse(true, 'Login berhasil', {
      user: {
        userId: user[0],
        username: user[1],
        namaLengkap: user[3],
        role: user[4]
      },
      redirectUrl: user[4] === USER_ROLES.ADMIN ? '?page=admin' : '?page=kasir'
    });
    
  } catch (error) {
    console.error('Error in handleLogin:', error);
    return createResponse(false, 'Terjadi kesalahan saat login: ' + error.message);
  }
}

/**
 * Handle logout request
 * @return {Object} Response object
 */
function handleLogout() {
  try {
    // Clear session
    const userProperties = PropertiesService.getUserProperties();
    userProperties.deleteProperty('SESSION_DATA');
    userProperties.deleteProperty('SESSION_ACTIVE');
    
    return createResponse(true, 'Logout berhasil', {
      redirectUrl: '?page=login'
    });
    
  } catch (error) {
    console.error('Error in handleLogout:', error);
    return createResponse(false, 'Terjadi kesalahan saat logout: ' + error.message);
  }
}

/**
 * Check if user is logged in
 * @return {boolean} True if logged in
 */
function isUserLoggedIn() {
  try {
    const userProperties = PropertiesService.getUserProperties();
    const sessionActive = userProperties.getProperty('SESSION_ACTIVE');
    const sessionData = userProperties.getProperty('SESSION_DATA');
    
    return sessionActive === 'true' && sessionData !== null;
  } catch (error) {
    console.error('Error checking login status:', error);
    return false;
  }
}

/**
 * Get current user session data
 * @return {Object|null} Session data or null if not logged in
 */
function getCurrentUser() {
  try {
    if (!isUserLoggedIn()) {
      return null;
    }
    
    const userProperties = PropertiesService.getUserProperties();
    const sessionData = userProperties.getProperty('SESSION_DATA');
    
    return JSON.parse(sessionData);
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if current user has specific role
 * @param {string} requiredRole - Role yang diperlukan
 * @return {boolean} True if user has role
 */
function hasRole(requiredRole) {
  try {
    const currentUser = getCurrentUser();
    if (!currentUser) {
      return false;
    }
    
    return currentUser.role === requiredRole;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
}

/**
 * Get current user info for client side
 * @return {Object} User info response
 */
function getCurrentUserInfo() {
  try {
    const currentUser = getCurrentUser();
    
    if (!currentUser) {
      return createResponse(false, 'User tidak login');
    }
    
    return createResponse(true, 'User info berhasil diambil', {
      user: {
        userId: currentUser.userId,
        username: currentUser.username,
        namaLengkap: currentUser.namaLengkap,
        role: currentUser.role,
        loginTime: currentUser.loginTime
      }
    });
    
  } catch (error) {
    console.error('Error getting user info:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Change password
 * @param {Object} params - Parameter (oldPassword, newPassword)
 * @return {Object} Response object
 */
function changePassword(params) {
  const oldPassword = params.oldPassword;
  const newPassword = params.newPassword;
  
  if (!oldPassword || !newPassword) {
    return createResponse(false, 'Password lama dan baru harus diisi');
  }
  
  if (newPassword.length < 6) {
    return createResponse(false, 'Password baru minimal 6 karakter');
  }
  
  try {
    const currentUser = getCurrentUser();
    if (!currentUser) {
      return createResponse(false, 'User tidak login');
    }
    
    // Get user data
    const userData = getDataById(SHEET_NAMES.USERS, currentUser.userId);
    if (!userData) {
      return createResponse(false, 'Data user tidak ditemukan');
    }
    
    // Verify old password
    const hashedOldPassword = hashPassword(oldPassword);
    if (userData[2] !== hashedOldPassword) {
      return createResponse(false, 'Password lama salah');
    }
    
    // Update password
    const hashedNewPassword = hashPassword(newPassword);
    userData[2] = hashedNewPassword;
    
    const success = updateDataById(SHEET_NAMES.USERS, currentUser.userId, userData);
    
    if (success) {
      return createResponse(true, 'Password berhasil diubah');
    } else {
      return createResponse(false, 'Gagal mengubah password');
    }
    
  } catch (error) {
    console.error('Error changing password:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}

/**
 * Validate session and get user data
 * Function ini dipanggil dari client untuk validasi session
 * @return {Object} Response object
 */
function validateSession() {
  try {
    if (!isUserLoggedIn()) {
      return createResponse(false, 'Session tidak valid', {
        redirectUrl: '?page=login'
      });
    }
    
    const currentUser = getCurrentUser();
    return createResponse(true, 'Session valid', {
      user: {
        userId: currentUser.userId,
        username: currentUser.username,
        namaLengkap: currentUser.namaLengkap,
        role: currentUser.role
      }
    });
    
  } catch (error) {
    console.error('Error validating session:', error);
    return createResponse(false, 'Terjadi kesalahan validasi session', {
      redirectUrl: '?page=login'
    });
  }
}

/**
 * Create new user (admin only)
 * @param {Object} params - User data
 * @return {Object} Response object
 */
function createUser(params) {
  try {
    // Check if current user is admin
    if (!hasRole(USER_ROLES.ADMIN)) {
      return createResponse(false, 'Akses ditolak. Hanya admin yang dapat membuat user baru');
    }
    
    const { username, password, namaLengkap, role, keterangan } = params;
    
    // Validation
    if (!username || !password || !namaLengkap || !role) {
      return createResponse(false, 'Username, password, nama lengkap, dan role harus diisi');
    }
    
    if (password.length < 6) {
      return createResponse(false, 'Password minimal 6 karakter');
    }
    
    if (![USER_ROLES.ADMIN, USER_ROLES.KASIR].includes(role)) {
      return createResponse(false, 'Role tidak valid');
    }
    
    // Check if username already exists
    const existingUser = searchData(SHEET_NAMES.USERS, 'B', username);
    if (existingUser.length > 0) {
      return createResponse(false, 'Username sudah digunakan');
    }
    
    // Create new user
    const userId = getNextId(SHEET_NAMES.USERS);
    const hashedPassword = hashPassword(password);
    
    const newUserData = [
      userId,
      username,
      hashedPassword,
      namaLengkap,
      role,
      keterangan || ''
    ];
    
    const success = insertData(SHEET_NAMES.USERS, newUserData);
    
    if (success) {
      return createResponse(true, 'User berhasil dibuat', {
        userId: userId,
        username: username,
        namaLengkap: namaLengkap,
        role: role
      });
    } else {
      return createResponse(false, 'Gagal membuat user');
    }
    
  } catch (error) {
    console.error('Error creating user:', error);
    return createResponse(false, 'Terjadi kesalahan: ' + error.message);
  }
}
