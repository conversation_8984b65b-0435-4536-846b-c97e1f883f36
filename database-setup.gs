/**
 * POS System Database Setup
 * Script untuk membuat struktur database di Google Sheets
 */

function setupDatabase() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Hapus sheet default jika ada
  try {
    const defaultSheet = ss.getSheetByName('Sheet1');
    if (defaultSheet) {
      ss.deleteSheet(defaultSheet);
    }
  } catch (e) {
    console.log('Sheet1 tidak ditemukan atau sudah dihapus');
  }
  
  // Setup semua sheets
  setupUsersSheet(ss);
  setupBarangSheet(ss);
  setupKategoriSheet(ss);
  setupSatuanSheet(ss);
  setupPenjualanSheet(ss);
  setupPenjualanItemSheet(ss);
  setupPelangganSheet(ss);
  setupMetodePembayaranSheet(ss);
  
  console.log('Database setup completed!');
}

function setupUsersSheet(ss) {
  let sheet = ss.getSheetByName('users');
  if (!sheet) {
    sheet = ss.insertSheet('users');
  }
  
  // Clear existing content
  sheet.clear();
  
  // Set headers
  const headers = [
    'user_id', 'username', 'password', 'nama_lengkap', 'role', 'keterangan'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#4285f4')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 80);  // user_id
  sheet.setColumnWidth(2, 120); // username
  sheet.setColumnWidth(3, 150); // password
  sheet.setColumnWidth(4, 200); // nama_lengkap
  sheet.setColumnWidth(5, 120); // role
  sheet.setColumnWidth(6, 200); // keterangan
  
  // Add sample data
  const sampleData = [
    [1, 'admin', hashPassword('admin123'), 'Administrator', 'administrator', 'Default admin user'],
    [2, 'kasir1', hashPassword('kasir123'), 'Kasir Satu', 'kasir', 'Default kasir user']
  ];
  
  sheet.getRange(2, 1, sampleData.length, headers.length).setValues(sampleData);
}

function setupBarangSheet(ss) {
  let sheet = ss.getSheetByName('barang');
  if (!sheet) {
    sheet = ss.insertSheet('barang');
  }
  
  sheet.clear();
  
  const headers = [
    'barang_id', 'barcode', 'nama_barang', 'kategori_id', 'satuan_id', 
    'harga_beli', 'harga_jual', 'stok', 'keterangan'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#34a853')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 80);  // barang_id
  sheet.setColumnWidth(2, 150); // barcode
  sheet.setColumnWidth(3, 250); // nama_barang
  sheet.setColumnWidth(4, 100); // kategori_id
  sheet.setColumnWidth(5, 100); // satuan_id
  sheet.setColumnWidth(6, 120); // harga_beli
  sheet.setColumnWidth(7, 120); // harga_jual
  sheet.setColumnWidth(8, 80);  // stok
  sheet.setColumnWidth(9, 200); // keterangan
}

function setupKategoriSheet(ss) {
  let sheet = ss.getSheetByName('kategori');
  if (!sheet) {
    sheet = ss.insertSheet('kategori');
  }
  
  sheet.clear();
  
  const headers = ['kategori_id', 'nama_kategori', 'keterangan'];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#ff9800')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 100); // kategori_id
  sheet.setColumnWidth(2, 200); // nama_kategori
  sheet.setColumnWidth(3, 300); // keterangan
  
  // Add sample categories
  const sampleData = [
    [1, 'Makanan', 'Kategori untuk produk makanan'],
    [2, 'Minuman', 'Kategori untuk produk minuman'],
    [3, 'Elektronik', 'Kategori untuk produk elektronik'],
    [4, 'Pakaian', 'Kategori untuk produk pakaian']
  ];
  
  sheet.getRange(2, 1, sampleData.length, headers.length).setValues(sampleData);
}

function setupSatuanSheet(ss) {
  let sheet = ss.getSheetByName('satuan');
  if (!sheet) {
    sheet = ss.insertSheet('satuan');
  }
  
  sheet.clear();
  
  const headers = ['satuan_id', 'nama_satuan', 'keterangan'];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#9c27b0')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 100); // satuan_id
  sheet.setColumnWidth(2, 150); // nama_satuan
  sheet.setColumnWidth(3, 300); // keterangan
  
  // Add sample units
  const sampleData = [
    [1, 'Pcs', 'Pieces/Buah'],
    [2, 'Kg', 'Kilogram'],
    [3, 'Liter', 'Liter'],
    [4, 'Pack', 'Paket/Kemasan'],
    [5, 'Box', 'Kotak/Dus']
  ];
  
  sheet.getRange(2, 1, sampleData.length, headers.length).setValues(sampleData);
}

function setupPenjualanSheet(ss) {
  let sheet = ss.getSheetByName('penjualan');
  if (!sheet) {
    sheet = ss.insertSheet('penjualan');
  }
  
  sheet.clear();
  
  const headers = [
    'penjualan_id', 'nomor_transaksi', 'tanggal_transaksi', 'user_id', 'pelanggan_id',
    'subtotal', 'diskon_persen', 'diskon_nominal', 'pajak_persen', 'pajak_nominal',
    'total_bayar', 'metode_pembayaran_id', 'jumlah_cash', 'jumlah_card', 'jumlah_digital',
    'kembalian', 'catatan', 'jam', 'tanggal'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#f44336')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  for (let i = 1; i <= headers.length; i++) {
    sheet.setColumnWidth(i, 120);
  }
}

function setupPenjualanItemSheet(ss) {
  let sheet = ss.getSheetByName('penjualan_item');
  if (!sheet) {
    sheet = ss.insertSheet('penjualan_item');
  }
  
  sheet.clear();
  
  const headers = [
    'penjualan_item_id', 'penjualan_id', 'barang_id', 'harga_beli', 'harga_jual',
    'quantity', 'diskon_item_persen', 'diskon_item_nominal', 'subtotal_item'
  ];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#795548')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  for (let i = 1; i <= headers.length; i++) {
    sheet.setColumnWidth(i, 120);
  }
}

function setupPelangganSheet(ss) {
  let sheet = ss.getSheetByName('pelanggan');
  if (!sheet) {
    sheet = ss.insertSheet('pelanggan');
  }
  
  sheet.clear();
  
  const headers = ['pelanggan_id', 'nama_pelanggan', 'telepon', 'email', 'alamat', 'keterangan'];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#607d8b')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 100); // pelanggan_id
  sheet.setColumnWidth(2, 200); // nama_pelanggan
  sheet.setColumnWidth(3, 150); // telepon
  sheet.setColumnWidth(4, 200); // email
  sheet.setColumnWidth(5, 300); // alamat
  sheet.setColumnWidth(6, 200); // keterangan
  
  // Add sample customer
  const sampleData = [
    [1, 'Pelanggan Umum', '', '', '', 'Default customer untuk transaksi umum']
  ];
  
  sheet.getRange(2, 1, sampleData.length, headers.length).setValues(sampleData);
}

function setupMetodePembayaranSheet(ss) {
  let sheet = ss.getSheetByName('metode_pembayaran');
  if (!sheet) {
    sheet = ss.insertSheet('metode_pembayaran');
  }
  
  sheet.clear();
  
  const headers = ['metode_pembayaran_id', 'nama_metode_pembayaran', 'keterangan'];
  
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  sheet.getRange(1, 1, 1, headers.length)
    .setBackground('#3f51b5')
    .setFontColor('white')
    .setFontWeight('bold');
  
  // Set column widths
  sheet.setColumnWidth(1, 150); // metode_pembayaran_id
  sheet.setColumnWidth(2, 200); // nama_metode_pembayaran
  sheet.setColumnWidth(3, 300); // keterangan
  
  // Add payment methods as requested
  const paymentMethods = [
    [1, 'Tunai', 'Pembayaran dengan uang tunai'],
    [2, 'Kartu Debit', 'Pembayaran dengan kartu debit'],
    [3, 'Kartu Kredit', 'Pembayaran dengan kartu kredit'],
    [4, 'OVO', 'Pembayaran dengan OVO'],
    [5, 'LinkAja', 'Pembayaran dengan LinkAja'],
    [6, 'DANA', 'Pembayaran dengan DANA'],
    [7, 'GoPay', 'Pembayaran dengan GoPay'],
    [8, 'ShopeePay', 'Pembayaran dengan ShopeePay'],
    [9, 'Transfer', 'Pembayaran dengan transfer bank'],
    [10, 'QRIS', 'Pembayaran dengan QRIS'],
    [11, 'Lainnya', 'Metode pembayaran lainnya'],
    [99, 'Kombinasi', 'Kombinasi beberapa metode pembayaran']
  ];
  
  sheet.getRange(2, 1, paymentMethods.length, headers.length).setValues(paymentMethods);
}

// Helper function untuk hash password (simple hash untuk demo)
function hashPassword(password) {
  return Utilities.base64Encode(Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_256, password));
}
