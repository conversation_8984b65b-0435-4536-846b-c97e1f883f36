<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .nav-tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 500px;
        }

        .tab-pane {
            display: none;
            padding: 2rem;
        }

        .tab-pane.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .section-header h2 {
            color: #333;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .alert {
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            display: none;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: #666;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>POS System - Admin Panel</h1>
        <div class="user-info">
            <span id="userInfo">Loading...</span>
            <button class="logout-btn" onclick="handleLogout()">Logout</button>
        </div>
    </div>

    <div class="container">
        <div id="alert" class="alert"></div>

        <!-- Statistics Dashboard -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3 id="totalBarang">-</h3>
                <p>Total Barang</p>
            </div>
            <div class="stat-card">
                <h3 id="totalKategori">-</h3>
                <p>Total Kategori</p>
            </div>
            <div class="stat-card">
                <h3 id="totalUsers">-</h3>
                <p>Total Users</p>
            </div>
            <div class="stat-card">
                <h3 id="totalPelanggan">-</h3>
                <p>Total Pelanggan</p>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('users')">Users</button>
            <button class="nav-tab" onclick="showTab('barang')">Barang</button>
            <button class="nav-tab" onclick="showTab('kategori')">Kategori</button>
            <button class="nav-tab" onclick="showTab('satuan')">Satuan</button>
            <button class="nav-tab" onclick="showTab('pelanggan')">Pelanggan</button>
            <button class="nav-tab" onclick="showTab('laporan')">Laporan</button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Users Tab -->
            <div id="users" class="tab-pane active">
                <div class="section-header">
                    <h2>Manajemen Users</h2>
                    <button class="btn btn-primary" onclick="showAddUserForm()">Tambah User</button>
                </div>
                
                <div id="addUserForm" style="display: none;">
                    <h3>Tambah User Baru</h3>
                    <form id="userForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="newUsername">Username</label>
                                <input type="text" id="newUsername" required>
                            </div>
                            <div class="form-group">
                                <label for="newPassword">Password</label>
                                <input type="password" id="newPassword" required>
                            </div>
                            <div class="form-group">
                                <label for="newNamaLengkap">Nama Lengkap</label>
                                <input type="text" id="newNamaLengkap" required>
                            </div>
                            <div class="form-group">
                                <label for="newRole">Role</label>
                                <select id="newRole" required>
                                    <option value="">Pilih Role</option>
                                    <option value="administrator">Administrator</option>
                                    <option value="kasir">Kasir</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newKeterangan">Keterangan</label>
                            <textarea id="newKeterangan" rows="3"></textarea>
                        </div>
                        <div style="margin-top: 1rem;">
                            <button type="submit" class="btn btn-success">Simpan</button>
                            <button type="button" class="btn btn-warning" onclick="hideAddUserForm()">Batal</button>
                        </div>
                    </form>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Nama Lengkap</th>
                                <th>Role</th>
                                <th>Keterangan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="6" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Other tabs will be added here -->
            <div id="barang" class="tab-pane">
                <div class="section-header">
                    <h2>Manajemen Barang</h2>
                    <button class="btn btn-primary">Tambah Barang</button>
                </div>
                <p>Fitur manajemen barang akan segera tersedia...</p>
            </div>

            <div id="kategori" class="tab-pane">
                <div class="section-header">
                    <h2>Manajemen Kategori</h2>
                    <button class="btn btn-primary">Tambah Kategori</button>
                </div>
                <p>Fitur manajemen kategori akan segera tersedia...</p>
            </div>

            <div id="satuan" class="tab-pane">
                <div class="section-header">
                    <h2>Manajemen Satuan</h2>
                    <button class="btn btn-primary">Tambah Satuan</button>
                </div>
                <p>Fitur manajemen satuan akan segera tersedia...</p>
            </div>

            <div id="pelanggan" class="tab-pane">
                <div class="section-header">
                    <h2>Manajemen Pelanggan</h2>
                    <button class="btn btn-primary">Tambah Pelanggan</button>
                </div>
                <p>Fitur manajemen pelanggan akan segera tersedia...</p>
            </div>

            <div id="laporan" class="tab-pane">
                <div class="section-header">
                    <h2>Laporan Penjualan</h2>
                </div>
                <p>Fitur laporan akan segera tersedia...</p>
            </div>
        </div>
    </div>

    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdmin();
        });

        function initializeAdmin() {
            // Validate session and load user info
            validateSession();
            loadStatistics();
            loadUsers();
        }

        function validateSession() {
            google.script.run
                .withSuccessHandler(function(response) {
                    if (response.success) {
                        document.getElementById('userInfo').textContent = 
                            `${response.data.user.namaLengkap} (${response.data.user.role})`;
                    } else {
                        window.location.href = '?page=login';
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Session validation failed:', error);
                    window.location.href = '?page=login';
                })
                .clientValidateSession();
        }

        function loadStatistics() {
            // Load dashboard statistics
            google.script.run
                .withSuccessHandler(function(response) {
                    if (response && response.success) {
                        const stats = response.data;
                        document.getElementById('totalBarang').textContent = stats.totalBarang || 0;
                        document.getElementById('totalKategori').textContent = stats.totalKategori || 0;
                        document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
                        document.getElementById('totalPelanggan').textContent = stats.totalPelanggan || 0;
                    } else {
                        console.error('Failed to load statistics:', response ? response.message : 'Unknown error');
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Failed to load statistics:', error);
                })
                .clientGetAdminStatistics();
        }

        function loadUsers() {
            google.script.run
                .withSuccessHandler(function(response) {
                    if (response && response.success) {
                        displayUsers(response.data);
                    } else {
                        showAlert(response ? response.message : 'Gagal memuat data users', 'error');
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Failed to load users:', error);
                    showAlert('Gagal memuat data users', 'error');
                })
                .clientGetAllUsers();
        }

        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">Tidak ada data users</td></tr>';
                return;
            }

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user[0]}</td>
                    <td>${user[1]}</td>
                    <td>${user[3]}</td>
                    <td>${user[4]}</td>
                    <td>${user[5] || '-'}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="editUser(${user[0]})">Edit</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser(${user[0]})">Hapus</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function showTab(tabName) {
            // Hide all tab panes
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab pane
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function showAddUserForm() {
            document.getElementById('addUserForm').style.display = 'block';
        }

        function hideAddUserForm() {
            document.getElementById('addUserForm').style.display = 'none';
            document.getElementById('userForm').reset();
        }

        function handleLogout() {
            if (confirm('Apakah Anda yakin ingin logout?')) {
                google.script.run
                    .withSuccessHandler(function(response) {
                        if (response.success) {
                            window.location.href = '?page=login';
                        }
                    })
                    .withFailureHandler(function(error) {
                        console.error('Logout failed:', error);
                    })
                    .handleLogout();
            }
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // User form submission
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const userData = {
                username: document.getElementById('newUsername').value,
                password: document.getElementById('newPassword').value,
                namaLengkap: document.getElementById('newNamaLengkap').value,
                role: document.getElementById('newRole').value,
                keterangan: document.getElementById('newKeterangan').value
            };

            google.script.run
                .withSuccessHandler(function(response) {
                    if (response.success) {
                        showAlert(response.message, 'success');
                        hideAddUserForm();
                        loadUsers();
                        loadStatistics();
                    } else {
                        showAlert(response.message, 'error');
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Failed to create user:', error);
                    showAlert('Gagal membuat user', 'error');
                })
                .createUser(userData);
        });
    </script>
</body>
</html>
